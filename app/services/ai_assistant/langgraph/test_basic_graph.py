"""
基础图测试

测试LangGraph的基本功能，验证状态传递和节点执行。
"""

import asyncio
import logging
from typing import Dict, Any
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.messages import HumanMessage, AIMessage

from app.services.ai_assistant.langgraph.state_definitions import UnifiedFitnessState
from app.services.ai_assistant.langgraph.utils.state_utils import StateUtils
from app.services.ai_assistant.langgraph.adapters.state_adapter import StateAdapter

logger = logging.getLogger(__name__)

async def simple_router_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """简单路由节点"""
    try:
        # 获取用户消息
        user_message = ""
        messages = state.get("messages", [])
        if messages:
            for msg in reversed(messages):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content
                    break

        # 改进的路由决策 - 更准确识别健身相关问题
        fitness_keywords = [
            "健身", "训练", "锻炼", "运动", "肌肉", "胸肌", "腹肌", "背肌", "腿部",
            "减肥", "增肌", "塑形", "力量", "有氧", "无氧", "卧推", "深蹲", "硬拉",
            "俯卧撑", "仰卧起坐", "跑步", "游泳", "瑜伽", "普拉提", "健身房",
            "哑铃", "杠铃", "器械", "营养", "蛋白质", "饮食", "计划", "方案"
        ]

        # 检查是否包含健身关键词
        contains_fitness_keywords = any(keyword in user_message for keyword in fitness_keywords)

        if contains_fitness_keywords:
            route = "enhanced"
            confidence = 0.9
            reasoning = f"检测到健身关键词，路由到增强处理器"
        else:
            route = "state_machine"
            confidence = 0.7
            reasoning = f"未检测到健身关键词，路由到状态机处理器"

        # 更新状态
        state["current_node"] = "router"
        state["routing_decision"] = {
            "route": route,
            "confidence": confidence,
            "reasoning": reasoning
        }

        logger.info(f"路由决策: {route} (置信度: {confidence:.2f})")
        return state

    except Exception as e:
        logger.error(f"路由节点失败: {str(e)}")
        state["current_node"] = "router"
        state["routing_decision"] = {
            "route": "state_machine",
            "confidence": 0.5,
            "reasoning": f"路由失败，使用默认: {str(e)}"
        }
        return state

async def simple_enhanced_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """简单增强处理节点 - 调用真实的健身建议处理器"""
    try:
        # 获取用户消息
        user_message = ""
        messages = state.get("messages", [])
        if messages:
            for msg in reversed(messages):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content
                    break

        # 调用真实的健身建议处理器
        try:
            from app.services.ai_assistant.intent.handlers.fitness_advice import FitnessAdviceHandler
            from app.services.ai_assistant.llm.proxy import LLMProxy
            from app.services.ai_assistant.knowledge.retriever import KnowledgeRetriever
            from app.services.ai_assistant.cache.service import default_cache_service

            # 创建处理器实例
            llm_proxy = LLMProxy()
            knowledge_retriever = KnowledgeRetriever()

            fitness_handler = FitnessAdviceHandler(
                llm_proxy=llm_proxy,
                knowledge_retriever=knowledge_retriever,
                cache_service=default_cache_service,
                use_bailian=True
            )

            # 准备用户信息
            user_info = state.get("user_profile", {})

            # 调用处理器
            handler_response = await fitness_handler.handle_intent(
                message=user_message,
                conversation_id=state.get("conversation_id", ""),
                user_info=user_info
            )

            # 提取响应内容
            if isinstance(handler_response, dict):
                response_content = handler_response.get("response_content", handler_response.get("response", ""))
                confidence = handler_response.get("confidence", 0.9)
                intent = handler_response.get("intent", "fitness_advice")
            else:
                response_content = str(handler_response)
                confidence = 0.9
                intent = "fitness_advice"

            logger.info(f"真实健身处理器响应: {response_content[:100]}...")

        except Exception as handler_error:
            logger.warning(f"健身处理器调用失败，使用默认响应: {str(handler_error)}")
            # 根据用户消息生成更智能的默认响应
            if "胸肌" in user_message:
                response_content = """关于胸肌训练，我为您推荐以下动作：

1. **俯卧撑** - 基础胸肌训练动作
   - 标准俯卧撑：3组，每组8-12次
   - 宽距俯卧撑：重点刺激胸肌外侧

2. **卧推** - 胸肌力量训练之王
   - 平板卧推：4组，每组6-10次
   - 上斜卧推：针对胸肌上部

3. **飞鸟动作** - 胸肌塑形
   - 哑铃飞鸟：3组，每组10-15次
   - 拉力器夹胸：高次数训练

**训练要点：**
- 动作要标准，感受胸肌发力
- 循序渐进增加重量
- 训练后充分拉伸

建议每周训练2-3次，给肌肉充分恢复时间。"""
            elif "健身" in user_message or "训练" in user_message:
                response_content = """欢迎开始您的健身之旅！作为您的专业健身AI助手，我可以为您提供：

🏋️ **训练计划制定**
- 根据您的目标和水平定制训练方案
- 力量训练、有氧运动、柔韧性训练

💪 **动作指导**
- 标准动作示范和要点
- 常见错误纠正
- 进阶变化动作

🥗 **营养建议**
- 饮食搭配建议
- 营养补充指导
- 减脂增肌饮食方案

请告诉我您的具体需求，比如：
- 想要训练哪个部位？
- 您的健身目标是什么？
- 目前的健身经验如何？

我会为您提供专业、个性化的健身指导！"""
            else:
                response_content = "您好！我是您的专业健身AI助手。请告诉我您想了解的健身相关问题，我会为您提供专业的建议和指导。"

            confidence = 0.8
            intent = "fitness_advice"

        # 更新状态
        state["current_node"] = "enhanced"
        state["processing_system"] = "enhanced"
        state["response_content"] = response_content
        state["confidence"] = confidence
        state["intent"] = intent

        # 添加AI响应
        ai_message = AIMessage(content=response_content)
        state["messages"].append(ai_message)

        logger.info("增强处理完成")
        return state

    except Exception as e:
        logger.error(f"增强处理失败: {str(e)}")
        error_response = "抱歉，增强处理器暂时无法处理您的请求。"
        state["current_node"] = "enhanced"
        state["processing_system"] = "enhanced"
        state["response_content"] = error_response
        StateUtils.set_error(state, f"增强处理失败: {str(e)}")

        ai_message = AIMessage(content=error_response)
        state["messages"].append(ai_message)

        return state

async def simple_state_machine_node(state: UnifiedFitnessState) -> UnifiedFitnessState:
    """简单状态机处理节点 - 提供通用对话处理"""
    try:
        # 获取用户消息
        user_message = ""
        messages = state.get("messages", [])
        if messages:
            for msg in reversed(messages):
                if hasattr(msg, 'type') and msg.type == "human":
                    user_message = msg.content
                    break

        # 尝试调用通用聊天处理器
        try:
            from app.services.ai_assistant.intent.handlers.general_chat import GeneralChatHandler
            from app.services.ai_assistant.llm.proxy import LLMProxy
            from app.services.ai_assistant.cache.service import default_cache_service

            # 创建处理器实例
            llm_proxy = LLMProxy()

            general_handler = GeneralChatHandler(
                llm_proxy=llm_proxy,
                cache_service=default_cache_service
            )

            # 准备用户信息
            user_info = state.get("user_profile", {})

            # 调用处理器
            handler_response = await general_handler.handle_intent(
                message=user_message,
                conversation_id=state.get("conversation_id", ""),
                user_info=user_info
            )

            # 提取响应内容
            if isinstance(handler_response, dict):
                response_content = handler_response.get("response_content", handler_response.get("response", ""))
                confidence = handler_response.get("confidence", 0.8)
                intent = handler_response.get("intent", "general_chat")
            else:
                response_content = str(handler_response)
                confidence = 0.8
                intent = "general_chat"

            logger.info(f"通用聊天处理器响应: {response_content[:100]}...")

        except Exception as handler_error:
            logger.warning(f"通用聊天处理器调用失败，使用智能默认响应: {str(handler_error)}")
            # 根据用户消息生成智能响应
            if "你好" in user_message or "hello" in user_message.lower():
                response_content = """您好！我是您的专业健身AI助手，很高兴为您服务！

我可以帮助您：
🏋️ 制定个性化训练计划
💪 指导正确的运动动作
🥗 提供营养饮食建议
📊 追踪健身进度
❓ 解答健身相关问题

请告诉我您想了解什么，我会为您提供专业的建议！"""
            elif "谢谢" in user_message or "thank" in user_message.lower():
                response_content = "不客气！很高兴能帮助您。如果您还有其他健身相关的问题，随时可以问我。祝您健身愉快，身体健康！💪"
            elif "再见" in user_message or "bye" in user_message.lower():
                response_content = "再见！记得坚持锻炼，保持健康的生活方式。期待下次为您服务！🌟"
            else:
                response_content = f"""我理解您说的是"{user_message}"。

作为您的健身AI助手，我专注于为您提供健身相关的帮助。如果您有任何关于：
- 运动训练
- 健身计划
- 营养饮食
- 健康生活方式

等方面的问题，我都很乐意为您解答！请告诉我您想了解什么。"""

            confidence = 0.7
            intent = "general_chat"

        # 更新状态
        state["current_node"] = "state_machine"
        state["processing_system"] = "state_machine"
        state["response_content"] = response_content
        state["confidence"] = confidence
        state["intent"] = intent

        # 添加AI响应
        ai_message = AIMessage(content=response_content)
        state["messages"].append(ai_message)

        logger.info("状态机处理完成")
        return state

    except Exception as e:
        logger.error(f"状态机处理失败: {str(e)}")
        error_response = "抱歉，我暂时无法处理您的请求。请稍后再试，或者告诉我您的健身相关问题，我会尽力帮助您。"
        state["current_node"] = "state_machine"
        state["processing_system"] = "state_machine"
        state["response_content"] = error_response
        StateUtils.set_error(state, f"状态机处理失败: {str(e)}")

        ai_message = AIMessage(content=error_response)
        state["messages"].append(ai_message)

        return state

def route_to_processor(state: UnifiedFitnessState) -> str:
    """路由条件函数"""
    try:
        routing_decision = state.get("routing_decision", {})
        route = routing_decision.get("route", "state_machine")

        # 验证路由
        valid_routes = ["enhanced", "state_machine"]
        if route not in valid_routes:
            logger.warning(f"无效路由: {route}，使用默认")
            return "state_machine"

        logger.info(f"路由到: {route}")
        return route

    except Exception as e:
        logger.error(f"路由条件失败: {str(e)}")
        return "state_machine"

class BasicTestGraph:
    """基础测试图"""

    def __init__(self):
        self.graph = None
        self.compiled_graph = None
        self.checkpointer = None
        self._initialized = False

    def initialize(self) -> bool:
        """初始化图"""
        try:
            # 创建状态图
            self.graph = StateGraph(UnifiedFitnessState)

            # 添加节点
            self.graph.add_node("router", simple_router_node)
            self.graph.add_node("enhanced", simple_enhanced_node)
            self.graph.add_node("state_machine", simple_state_machine_node)

            # 添加边
            self.graph.set_entry_point("router")
            self.graph.add_conditional_edges(
                "router",
                route_to_processor,
                {
                    "enhanced": "enhanced",
                    "state_machine": "state_machine"
                }
            )
            self.graph.add_edge("enhanced", END)
            self.graph.add_edge("state_machine", END)

            # 使用内存检查点
            self.checkpointer = MemorySaver()

            # 编译图
            self.compiled_graph = self.graph.compile(checkpointer=self.checkpointer)

            self._initialized = True
            logger.info("基础测试图初始化成功")
            return True

        except Exception as e:
            logger.error(f"基础测试图初始化失败: {str(e)}")
            return False

    async def process_message(self, message: str, conversation_id: str, user_info: Dict[str, Any]) -> Dict[str, Any]:
        """处理消息"""
        try:
            if not self._initialized:
                if not self.initialize():
                    raise Exception("图初始化失败")

            # 创建初始状态
            initial_state = StateAdapter.to_unified_state(
                message=message,
                conversation_id=conversation_id,
                user_info=user_info
            )

            # 配置
            config = {
                "configurable": {
                    "thread_id": conversation_id,
                    "checkpoint_ns": "basic_test"
                }
            }

            # 执行图
            result = await self.compiled_graph.ainvoke(initial_state, config=config)

            # 转换为API响应
            api_response = StateAdapter.create_api_response(result)

            logger.info(f"基础图执行完成: {conversation_id}")
            return api_response

        except Exception as e:
            logger.error(f"基础图执行失败: {str(e)}")
            return {
                "success": False,
                "response": f"处理失败: {str(e)}",
                "intent_type": "error",
                "confidence": 0.0,
                "conversation_id": conversation_id,
                "error": str(e)
            }

# 全局实例
basic_test_graph = BasicTestGraph()

async def test_basic_graph_functionality():
    """测试基础图功能"""
    try:
        print("开始测试基础图功能...")

        # 测试健身相关消息
        result1 = await basic_test_graph.process_message(
            message="你好，我想了解健身训练",
            conversation_id="test_conv_1",
            user_info={"user_id": "test_user", "nickname": "测试用户"}
        )

        print("测试1 - 健身消息:")
        print(f"  成功: {result1.get('success', False)}")
        print(f"  响应: {result1.get('response', '')[:100]}...")
        print(f"  意图: {result1.get('intent_type', 'unknown')}")
        print(f"  置信度: {result1.get('confidence', 0.0):.2f}")
        print(f"  处理系统: {result1.get('processing_info', {}).get('system', 'unknown')}")

        # 测试一般消息
        result2 = await basic_test_graph.process_message(
            message="你好，今天天气怎么样？",
            conversation_id="test_conv_2",
            user_info={"user_id": "test_user", "nickname": "测试用户"}
        )

        print("\n测试2 - 一般消息:")
        print(f"  成功: {result2.get('success', False)}")
        print(f"  响应: {result2.get('response', '')[:100]}...")
        print(f"  意图: {result2.get('intent_type', 'unknown')}")
        print(f"  置信度: {result2.get('confidence', 0.0):.2f}")
        print(f"  处理系统: {result2.get('processing_info', {}).get('system', 'unknown')}")

        # 检查结果
        success = (
            result1.get('success', False) and
            result2.get('success', False) and
            result1.get('processing_info', {}).get('system') == 'enhanced' and
            result2.get('processing_info', {}).get('system') == 'state_machine'
        )

        print(f"\n✅ 基础图测试{'成功' if success else '失败'}")
        return success

    except Exception as e:
        print(f"❌ 基础图测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_basic_graph_functionality())
